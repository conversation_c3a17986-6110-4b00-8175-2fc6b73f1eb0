"use server";

import { cache } from "react";
import { prisma } from "@/lib/prisma";

/**
 * Check if a member exists by email for registration
 */
export async function checkMemberByEmail(email: string) {
  try {
    // Check if the email exists in the members table
    const members = await prisma.member.findMany({
      where: { email },
      select: {
        id: true,
        email: true,
        tenantMembers: {
          select: {
            tenantId: true,
          },
        },
      },
    });

    if (!members || members.length === 0) {
      return {
        success: false,
        error:
          "No member account found with this email. Please contact your gym administrator.",
        data: null,
      };
    }

    return {
      success: true,
      data: members,
    };
  } catch (error) {
    console.error("Error checking member by email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to check member",
      data: null,
    };
  }
}

/**
 * Get member subscriptions with plan details for dashboard
 * This function is cached to improve performance
 */
export const getMemberDashboardSubscriptions = cache(
  async (memberId: string, tenantId: string) => {
    try {
      // Get subscriptions for the member in the specific tenant
      const subscriptions = await prisma.subscription.findMany({
        where: {
          memberId: memberId,
          plan: {
            tenantId: tenantId,
          },
        },
        include: {
          plan: true,
          member: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Convert Decimal objects to numbers for client serialization
      const serializedSubscriptions = subscriptions.map((subscription) => ({
        ...subscription,
        plan: {
          ...subscription.plan,
          price: Number(subscription.plan.price),
        },
        // Transform field names for compatibility with existing client code
        subscription_plans: {
          id: subscription.plan.id,
          name: subscription.plan.name,
          price: Number(subscription.plan.price),
          billing_cycle: subscription.plan.billingCycle,
          tenant_id: subscription.plan.tenantId,
        },
        start_date: subscription.startDate,
        end_date: subscription.endDate,
        auto_renew: subscription.autoRenew,
      }));

      return { success: true, data: serializedSubscriptions };
    } catch (error) {
      console.error("Error getting member dashboard subscriptions:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get member subscriptions",
        data: [],
      };
    }
  }
);

/**
 * Get member payment history for dashboard
 * This function is cached to improve performance
 */
export const getMemberDashboardPayments = cache(
  async (memberId: string, tenantId: string) => {
    try {
      // Get payments for the member in the specific tenant
      const payments = await prisma.payment.findMany({
        where: {
          subscription: {
            memberId: memberId,
            plan: {
              tenantId: tenantId,
            },
          },
        },
        include: {
          subscription: {
            include: {
              plan: true,
              member: true,
            },
          },
        },
        orderBy: {
          paymentDate: "desc",
        },
      });

      // Convert Decimal objects to numbers and transform for compatibility
      const serializedPayments = payments.map((payment) => ({
        ...payment,
        amount: Number(payment.amount),
        payment_date: payment.paymentDate,
        payment_method: payment.paymentMethod,
        transaction_id: payment.transactionId,
        subscription_id: payment.subscriptionId,
        subscriptions: {
          ...payment.subscription,
          member_id: payment.subscription.memberId,
          plan_id: payment.subscription.planId,
          start_date: payment.subscription.startDate,
          end_date: payment.subscription.endDate,
          auto_renew: payment.subscription.autoRenew,
          subscription_plans: {
            id: payment.subscription.plan.id,
            name: payment.subscription.plan.name,
            price: Number(payment.subscription.plan.price),
            billing_cycle: payment.subscription.plan.billingCycle,
            tenant_id: payment.subscription.plan.tenantId,
          },
        },
      }));

      return { success: true, data: serializedPayments };
    } catch (error) {
      console.error("Error getting member dashboard payments:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get member payments",
        data: [],
      };
    }
  }
);

/**
 * Get member dashboard data (subscriptions and payments)
 * This function is cached to improve performance
 */
export const getMemberDashboardData = cache(
  async (memberId: string, tenantId: string) => {
    try {
      // Get both subscriptions and payments in parallel
      const [subscriptionsResult, paymentsResult] = await Promise.all([
        getMemberDashboardSubscriptions(memberId, tenantId),
        getMemberDashboardPayments(memberId, tenantId),
      ]);

      return {
        success: true,
        data: {
          subscriptions: subscriptionsResult.success
            ? subscriptionsResult.data
            : [],
          payments: paymentsResult.success ? paymentsResult.data : [],
        },
        errors: {
          subscriptions: subscriptionsResult.success
            ? null
            : subscriptionsResult.error,
          payments: paymentsResult.success ? null : paymentsResult.error,
        },
      };
    } catch (error) {
      console.error("Error getting member dashboard data:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get member dashboard data",
        data: {
          subscriptions: [],
          payments: [],
        },
        errors: {
          subscriptions: "Failed to load subscriptions",
          payments: "Failed to load payments",
        },
      };
    }
  }
);
