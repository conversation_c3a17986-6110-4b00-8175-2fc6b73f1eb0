"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";
import { verifyTenantAccess } from "@/lib/auth-prisma";

// Define the payment schema for validation
const paymentSchema = z.object({
  subscription_id: z.string().min(1, "Subscription ID is required"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  status: z.enum(["pending", "succeeded", "failed", "refunded"]),
  payment_date: z.string(),
  payment_method: z.enum([
    "credit_card",
    "debit_card",
    "bank_transfer",
    "cash",
    "other",
  ]),
  transaction_id: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentData = z.infer<typeof paymentSchema>;

/**
 * Create a new payment
 */
export async function createPayment(data: PaymentData) {
  try {
    // Validate the data
    const validatedData = paymentSchema.parse(data);

    // Get the subscription with plan details using Prisma
    const subscription = await prisma.subscription.findUnique({
      where: { id: validatedData.subscription_id },
      include: {
        plan: true,
        member: true,
      },
    });

    if (!subscription) {
      throw new Error("Subscription not found");
    }

    // Verify tenant access
    await verifyTenantAccess(subscription.plan.tenantId);

    // Convert the data to match Prisma field names
    const paymentData = {
      subscriptionId: validatedData.subscription_id,
      amount: validatedData.amount,
      status: validatedData.status,
      paymentDate: new Date(validatedData.payment_date),
      paymentMethod: validatedData.payment_method,
      transactionId: validatedData.transaction_id,
    };

    // Create the payment using Prisma
    const payment = await prisma.payment.create({
      data: paymentData,
      include: {
        subscription: {
          include: {
            member: true,
            plan: true,
          },
        },
      },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedPayment = {
      ...payment,
      amount: Number(payment.amount),
      subscription: {
        ...payment.subscription,
        plan: {
          ...payment.subscription.plan,
          price: Number(payment.subscription.plan.price),
        },
      },
    };

    // Revalidate the payments page to update the UI
    revalidatePath(`/tenant/${subscription.plan.tenantId}/payments`);
    revalidatePath(
      `/tenant/${subscription.plan.tenantId}/subscriptions/${validatedData.subscription_id}`
    );

    return { success: true, data: serializedPayment };
  } catch (error) {
    console.error("Error creating payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create payment",
    };
  }
}

/**
 * Update an existing payment
 */
export async function updatePayment(id: string, data: Partial<PaymentData>) {
  try {
    // Get the current payment with subscription and plan details using Prisma
    const currentPayment = await prisma.payment.findUnique({
      where: { id },
      include: {
        subscription: {
          include: {
            plan: true,
            member: true,
          },
        },
      },
    });

    if (!currentPayment) {
      throw new Error("Payment not found");
    }

    // Verify tenant access
    await verifyTenantAccess(currentPayment.subscription.plan.tenantId);

    // Convert the update data to match Prisma field names
    const updateData: any = {};
    if (data.amount !== undefined) updateData.amount = data.amount;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.payment_date !== undefined)
      updateData.paymentDate = new Date(data.payment_date);
    if (data.payment_method !== undefined)
      updateData.paymentMethod = data.payment_method;
    if (data.transaction_id !== undefined)
      updateData.transactionId = data.transaction_id;

    // Update the payment using Prisma
    const payment = await prisma.payment.update({
      where: { id },
      data: updateData,
      include: {
        subscription: {
          include: {
            member: true,
            plan: true,
          },
        },
      },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedPayment = {
      ...payment,
      amount: Number(payment.amount),
      subscription: {
        ...payment.subscription,
        plan: {
          ...payment.subscription.plan,
          price: Number(payment.subscription.plan.price),
        },
      },
    };

    // Revalidate the payment pages to update the UI
    revalidatePath(
      `/tenant/${currentPayment.subscription.plan.tenantId}/payments`
    );
    revalidatePath(
      `/tenant/${currentPayment.subscription.plan.tenantId}/payments/${id}`
    );
    revalidatePath(
      `/tenant/${currentPayment.subscription.plan.tenantId}/subscriptions/${currentPayment.subscriptionId}`
    );

    return { success: true, data: serializedPayment };
  } catch (error) {
    console.error("Error updating payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update payment",
    };
  }
}

/**
 * Process a payment (simulate payment processing)
 */
export async function processPayment(subscriptionId: string, amount: number) {
  try {
    // Create a payment with pending status
    const paymentData = {
      subscription_id: subscriptionId,
      amount,
      status: "pending" as const,
      payment_date: new Date().toISOString(),
      payment_method: "credit_card" as const,
      transaction_id: `txn_${Math.random().toString(36).substring(2, 10)}`,
    };

    // Create the payment
    const result = await createPayment(paymentData);

    if (!result.success || !result.data) {
      throw new Error(result.error || "Failed to create payment");
    }

    // Simulate payment processing
    // In a real application, this would call a payment gateway
    const paymentId = result.data.id;

    // Update the payment status to succeeded
    const updateResult = await updatePayment(paymentId, {
      status: "succeeded",
    });

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    return { success: true, data: updateResult.data };
  } catch (error) {
    console.error("Error processing payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to process payment",
    };
  }
}

/**
 * Get all payments for a tenant
 * This function is cached to improve performance
 */
export const getPayments = cache(async (tenantId: string) => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get payments with proper tenant filtering using Prisma
    const payments = await prisma.payment.findMany({
      where: {
        subscription: {
          plan: {
            tenantId: tenantId,
          },
        },
      },
      include: {
        subscription: {
          include: {
            member: true,
            plan: true,
          },
        },
      },
      orderBy: {
        paymentDate: "desc",
      },
    });

    // Convert Decimal objects to numbers for client serialization and add compatibility fields
    const serializedPayments = payments.map((payment) => ({
      ...payment,
      amount: Number(payment.amount),
      payment_date: payment.paymentDate,
      payment_method: payment.paymentMethod,
      transaction_id: payment.transactionId,
      subscription_id: payment.subscriptionId,
      subscription: {
        ...payment.subscription,
        plan: {
          ...payment.subscription.plan,
          price: Number(payment.subscription.plan.price),
        },
      },
      // Add compatibility fields for existing client code
      subscriptions: {
        ...payment.subscription,
        id: payment.subscription.id,
        member_id: payment.subscription.memberId,
        plan_id: payment.subscription.planId,
        start_date: payment.subscription.startDate,
        end_date: payment.subscription.endDate,
        auto_renew: payment.subscription.autoRenew,
        status: payment.subscription.status,
        members: {
          id: payment.subscription.member.id,
          first_name: payment.subscription.member.firstName,
          last_name: payment.subscription.member.lastName,
          email: payment.subscription.member.email,
          phone: payment.subscription.member.phone,
        },
        subscription_plans: {
          id: payment.subscription.plan.id,
          name: payment.subscription.plan.name,
          price: Number(payment.subscription.plan.price),
          billing_cycle: payment.subscription.plan.billingCycle,
          tenant_id: payment.subscription.plan.tenantId,
        },
      },
    }));

    return { success: true, data: serializedPayments };
  } catch (error) {
    console.error("Error getting payments:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get payments",
      data: [],
    };
  }
});

/**
 * Get a payment by ID
 * This function is cached to improve performance
 */
export const getPaymentById = cache(async (id: string) => {
  try {
    // Get the payment with all related data using Prisma
    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        subscription: {
          include: {
            member: true,
            plan: true,
          },
        },
      },
    });

    if (!payment) {
      return {
        success: false,
        error: "Payment not found",
        data: null,
      };
    }

    // Convert Decimal objects to numbers for client serialization and add compatibility fields
    const serializedPayment = {
      ...payment,
      amount: Number(payment.amount),
      payment_date: payment.paymentDate,
      payment_method: payment.paymentMethod,
      transaction_id: payment.transactionId,
      subscription_id: payment.subscriptionId,
      subscription: {
        ...payment.subscription,
        plan: {
          ...payment.subscription.plan,
          price: Number(payment.subscription.plan.price),
        },
      },
      // Add compatibility fields for existing client code
      subscriptions: {
        ...payment.subscription,
        id: payment.subscription.id,
        member_id: payment.subscription.memberId,
        plan_id: payment.subscription.planId,
        start_date: payment.subscription.startDate,
        end_date: payment.subscription.endDate,
        auto_renew: payment.subscription.autoRenew,
        status: payment.subscription.status,
        members: {
          id: payment.subscription.member.id,
          first_name: payment.subscription.member.firstName,
          last_name: payment.subscription.member.lastName,
          email: payment.subscription.member.email,
          phone: payment.subscription.member.phone,
        },
        subscription_plans: {
          id: payment.subscription.plan.id,
          name: payment.subscription.plan.name,
          price: Number(payment.subscription.plan.price),
          billing_cycle: payment.subscription.plan.billingCycle,
          tenant_id: payment.subscription.plan.tenantId,
        },
      },
    };

    return { success: true, data: serializedPayment };
  } catch (error) {
    console.error("Error getting payment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get payment",
      data: null,
    };
  }
});

/**
 * Get all payments for a subscription
 * This function is cached to improve performance
 */
export const getSubscriptionPayments = cache(async (subscriptionId: string) => {
  try {
    // Get all payments for the subscription using Prisma
    const payments = await prisma.payment.findMany({
      where: { subscriptionId },
      orderBy: { paymentDate: "desc" },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedPayments = payments.map((payment) => ({
      ...payment,
      amount: Number(payment.amount),
    }));

    return { success: true, data: serializedPayments };
  } catch (error) {
    console.error("Error getting subscription payments:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to get subscription payments",
      data: [],
    };
  }
});
