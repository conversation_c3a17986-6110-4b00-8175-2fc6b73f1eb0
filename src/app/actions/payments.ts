"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";
import { verifyTenantAccess } from "@/lib/auth-prisma";

// Define the payment schema for validation
const paymentSchema = z.object({
  subscription_id: z.string().min(1, "Subscription ID is required"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  status: z.enum(["pending", "succeeded", "failed", "refunded"]),
  payment_date: z.string(),
  payment_method: z.enum([
    "credit_card",
    "debit_card",
    "bank_transfer",
    "cash",
    "other",
  ]),
  transaction_id: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentData = z.infer<typeof paymentSchema>;

/**
 * Create a new payment
 */
export async function createPayment(data: PaymentData) {
  try {
    // Validate the data
    const validatedData = paymentSchema.parse(data);

    // Create a Supabase client
    const supabase = await createClient();

    // Get the subscription to get the member_id
    const { data: subscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .select("member_id")
      .eq("id", validatedData.subscription_id)
      .single();

    if (subscriptionError || !subscription) {
      throw new Error(
        `Failed to find subscription: ${
          subscriptionError?.message || "Subscription not found"
        }`
      );
    }

    // Get the subscription plan to get the tenant_id
    const { data: subscriptionDetails, error: subscriptionDetailsError } =
      await supabase
        .from("subscriptions")
        .select("plan_id")
        .eq("id", validatedData.subscription_id)
        .single();

    if (subscriptionDetailsError || !subscriptionDetails) {
      throw new Error(
        `Failed to get subscription details: ${
          subscriptionDetailsError?.message || "Subscription details not found"
        }`
      );
    }

    // Get the tenant_id from the subscription plan
    const { data: plan, error: planError } = await supabase
      .from("subscription_plans")
      .select("tenant_id")
      .eq("id", subscriptionDetails.plan_id)
      .single();

    if (planError || !plan) {
      throw new Error(
        `Failed to find subscription plan: ${
          planError?.message || "Subscription plan not found"
        }`
      );
    }

    // Use the plan's tenant_id
    const tenantMember = { tenant_id: plan.tenant_id };

    // Insert the payment
    const { data: payment, error } = await supabase
      .from("payments")
      .insert([validatedData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create payment: ${error.message}`);
    }

    // Revalidate the payments page to update the UI
    revalidatePath(`/tenant/${tenantMember.tenant_id}/payments`);
    revalidatePath(
      `/tenant/${tenantMember.tenant_id}/subscriptions/${validatedData.subscription_id}`
    );

    return { success: true, data: payment };
  } catch (error) {
    console.error("Error creating payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create payment",
    };
  }
}

/**
 * Update an existing payment
 */
export async function updatePayment(id: string, data: Partial<PaymentData>) {
  try {
    // Create a Supabase client
    const supabase = await createClient();

    // Get the current payment to get the subscription_id
    const { data: currentPayment, error: fetchError } = await supabase
      .from("payments")
      .select("subscription_id")
      .eq("id", id)
      .single();

    if (fetchError || !currentPayment) {
      throw new Error(
        `Failed to find payment: ${fetchError?.message || "Payment not found"}`
      );
    }

    // Get the subscription to get the member_id
    const { data: subscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .select("member_id")
      .eq("id", currentPayment.subscription_id)
      .single();

    if (subscriptionError || !subscription) {
      throw new Error(
        `Failed to find subscription: ${
          subscriptionError?.message || "Subscription not found"
        }`
      );
    }

    // Get the subscription plan to get the tenant_id
    const { data: subscriptionDetails, error: subscriptionDetailsError } =
      await supabase
        .from("subscriptions")
        .select("plan_id")
        .eq("id", currentPayment.subscription_id)
        .single();

    if (subscriptionDetailsError || !subscriptionDetails) {
      throw new Error(
        `Failed to get subscription details: ${
          subscriptionDetailsError?.message || "Subscription details not found"
        }`
      );
    }

    // Get the tenant_id from the subscription plan
    const { data: plan, error: planError } = await supabase
      .from("subscription_plans")
      .select("tenant_id")
      .eq("id", subscriptionDetails.plan_id)
      .single();

    if (planError || !plan) {
      throw new Error(
        `Failed to find subscription plan: ${
          planError?.message || "Subscription plan not found"
        }`
      );
    }

    // Use the plan's tenant_id
    const tenantMember = { tenant_id: plan.tenant_id };

    // Update the payment
    const { data: payment, error } = await supabase
      .from("payments")
      .update(data)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update payment: ${error.message}`);
    }

    // Revalidate the payment pages to update the UI
    revalidatePath(`/tenant/${tenantMember.tenant_id}/payments`);
    revalidatePath(`/tenant/${tenantMember.tenant_id}/payments/${id}`);
    revalidatePath(
      `/tenant/${tenantMember.tenant_id}/subscriptions/${currentPayment.subscription_id}`
    );

    return { success: true, data: payment };
  } catch (error) {
    console.error("Error updating payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update payment",
    };
  }
}

/**
 * Process a payment (simulate payment processing)
 */
export async function processPayment(subscriptionId: string, amount: number) {
  try {
    // Create a payment with pending status
    const paymentData = {
      subscription_id: subscriptionId,
      amount,
      status: "pending" as const,
      payment_date: new Date().toISOString(),
      payment_method: "credit_card" as const,
      transaction_id: `txn_${Math.random().toString(36).substring(2, 10)}`,
    };

    // Create the payment
    const result = await createPayment(paymentData);

    if (!result.success || !result.data) {
      throw new Error(result.error || "Failed to create payment");
    }

    // Simulate payment processing
    // In a real application, this would call a payment gateway
    const paymentId = result.data.id;

    // Update the payment status to succeeded
    const updateResult = await updatePayment(paymentId, {
      status: "succeeded",
    });

    if (!updateResult.success) {
      throw new Error(updateResult.error);
    }

    return { success: true, data: updateResult.data };
  } catch (error) {
    console.error("Error processing payment:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to process payment",
    };
  }
}

/**
 * Get all payments for a tenant
 * This function is cached to improve performance
 */
export const getPayments = cache(async (tenantId: string) => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get payments with proper tenant filtering using Prisma
    const payments = await prisma.payment.findMany({
      where: {
        subscription: {
          plan: {
            tenantId: tenantId,
          },
        },
      },
      include: {
        subscription: {
          include: {
            member: true,
            plan: true,
          },
        },
      },
      orderBy: {
        paymentDate: "desc",
      },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedPayments = payments.map((payment) => ({
      ...payment,
      amount: Number(payment.amount),
      subscription: {
        ...payment.subscription,
        plan: {
          ...payment.subscription.plan,
          price: Number(payment.subscription.plan.price),
        },
      },
    }));

    return { success: true, data: serializedPayments };
  } catch (error) {
    console.error("Error getting payments:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get payments",
      data: [],
    };
  }
});

/**
 * Get a payment by ID
 * This function is cached to improve performance
 */
export const getPaymentById = cache(async (id: string) => {
  try {
    // Get the payment with all related data using Prisma
    const payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        subscription: {
          include: {
            member: true,
            plan: true,
          },
        },
      },
    });

    if (!payment) {
      return {
        success: false,
        error: "Payment not found",
        data: null,
      };
    }

    // Convert Decimal objects to numbers for client serialization
    const serializedPayment = {
      ...payment,
      amount: Number(payment.amount),
      subscription: {
        ...payment.subscription,
        plan: {
          ...payment.subscription.plan,
          price: Number(payment.subscription.plan.price),
        },
      },
    };

    return { success: true, data: serializedPayment };
  } catch (error) {
    console.error("Error getting payment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get payment",
      data: null,
    };
  }
});

/**
 * Get all payments for a subscription
 * This function is cached to improve performance
 */
export const getSubscriptionPayments = cache(async (subscriptionId: string) => {
  try {
    // Get all payments for the subscription using Prisma
    const payments = await prisma.payment.findMany({
      where: { subscriptionId },
      orderBy: { paymentDate: "desc" },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedPayments = payments.map((payment) => ({
      ...payment,
      amount: Number(payment.amount),
    }));

    return { success: true, data: serializedPayments };
  } catch (error) {
    console.error("Error getting subscription payments:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to get subscription payments",
      data: [],
    };
  }
});
