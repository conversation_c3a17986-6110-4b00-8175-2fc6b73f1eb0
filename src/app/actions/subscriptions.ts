"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";
import { verifyTenantAccess } from "@/lib/auth-prisma";

// Define the subscription schema for validation
const subscriptionSchema = z.object({
  member_id: z.string().min(1, "Member ID is required"),
  plan_id: z.string().min(1, "Plan ID is required"),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  status: z.enum([
    "active",
    "inactive",
    "pending",
    "cancelled",
    "canceled",
    "expired",
  ]),
  auto_renew: z.boolean().default(true),
  price_override: z.number().optional(),
  notes: z.string().optional(),
  current_tenant_id: z.string().min(1, "Tenant ID is required").optional(), // Optional tenant ID to help with member lookup
});

type SubscriptionData = z.infer<typeof subscriptionSchema>;

/**
 * Create a new subscription
 */
export async function createSubscription(data: SubscriptionData) {
  try {
    // Validate the data
    const validatedData = subscriptionSchema.parse(data);

    // Get the subscription plan to verify it belongs to the correct tenant using Prisma
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: validatedData.plan_id },
    });

    if (!plan) {
      throw new Error("Subscription plan not found");
    }

    // Verify tenant access
    await verifyTenantAccess(plan.tenantId);

    // Verify that the member belongs to the same tenant as the plan
    const tenantMember = await prisma.tenantMember.findFirst({
      where: {
        memberId: validatedData.member_id,
        tenantId: plan.tenantId,
      },
    });

    if (!tenantMember) {
      throw new Error(
        `Member does not belong to the same tenant as the subscription plan. Please ensure the member is part of this tenant.`
      );
    }

    // Remove current_tenant_id from validatedData before inserting
    const { current_tenant_id, ...subscriptionData } = validatedData;

    // Convert the data to match Prisma field names
    const prismaData = {
      memberId: subscriptionData.member_id,
      planId: subscriptionData.plan_id,
      startDate: subscriptionData.start_date
        ? new Date(subscriptionData.start_date)
        : new Date(),
      endDate: subscriptionData.end_date
        ? new Date(subscriptionData.end_date)
        : null,
      status: subscriptionData.status,
      autoRenew: subscriptionData.auto_renew ?? true,
    };

    // Create the subscription using Prisma
    const subscription = await prisma.subscription.create({
      data: prismaData,
      include: {
        member: true,
        plan: true,
      },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedSubscription = {
      ...subscription,
      plan: {
        ...subscription.plan,
        price: Number(subscription.plan.price),
      },
    };

    // Revalidate the subscriptions page to update the UI
    revalidatePath(`/tenant/${plan.tenantId}/subscriptions`);
    revalidatePath(
      `/tenant/${plan.tenantId}/members/${validatedData.member_id}`
    );

    return { success: true, data: serializedSubscription };
  } catch (error) {
    console.error("Error creating subscription:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create subscription",
    };
  }
}

/**
 * Update an existing subscription
 */
export async function updateSubscription(
  id: string,
  data: Partial<SubscriptionData>
) {
  try {
    // Get the current subscription with plan details using Prisma
    const currentSubscription = await prisma.subscription.findUnique({
      where: { id },
      include: {
        plan: true,
        member: true,
      },
    });

    if (!currentSubscription) {
      throw new Error("Subscription not found");
    }

    // Verify tenant access
    await verifyTenantAccess(currentSubscription.plan.tenantId);

    // Convert the update data to match Prisma field names
    const updateData: any = {};
    if (data.member_id !== undefined) updateData.memberId = data.member_id;
    if (data.plan_id !== undefined) updateData.planId = data.plan_id;
    if (data.start_date !== undefined)
      updateData.startDate = new Date(data.start_date);
    if (data.end_date !== undefined)
      updateData.endDate = data.end_date ? new Date(data.end_date) : null;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.auto_renew !== undefined) updateData.autoRenew = data.auto_renew;

    // Update the subscription using Prisma
    const subscription = await prisma.subscription.update({
      where: { id },
      data: updateData,
      include: {
        member: true,
        plan: true,
      },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedSubscription = {
      ...subscription,
      plan: {
        ...subscription.plan,
        price: Number(subscription.plan.price),
      },
    };

    // Revalidate the subscription pages to update the UI
    revalidatePath(
      `/tenant/${currentSubscription.plan.tenantId}/subscriptions`
    );
    revalidatePath(
      `/tenant/${currentSubscription.plan.tenantId}/subscriptions/${id}`
    );
    revalidatePath(
      `/tenant/${currentSubscription.plan.tenantId}/members/${currentSubscription.memberId}`
    );

    return { success: true, data: serializedSubscription };
  } catch (error) {
    console.error("Error updating subscription:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update subscription",
    };
  }
}

/**
 * Cancel a subscription
 */
export async function cancelSubscription(id: string) {
  try {
    // Get the current subscription with plan details using Prisma
    const currentSubscription = await prisma.subscription.findUnique({
      where: { id },
      include: {
        plan: true,
        member: true,
      },
    });

    if (!currentSubscription) {
      throw new Error("Subscription not found");
    }

    // Verify tenant access
    await verifyTenantAccess(currentSubscription.plan.tenantId);

    // Update the subscription status to cancelled
    // Note: We use "cancelled" (British spelling) for consistency in the database
    const subscription = await prisma.subscription.update({
      where: { id },
      data: {
        status: "cancelled",
        autoRenew: false,
      },
      include: {
        member: true,
        plan: true,
      },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedSubscription = {
      ...subscription,
      plan: {
        ...subscription.plan,
        price: Number(subscription.plan.price),
      },
    };

    // Revalidate the subscription pages to update the UI
    revalidatePath(
      `/tenant/${currentSubscription.plan.tenantId}/subscriptions`
    );
    revalidatePath(
      `/tenant/${currentSubscription.plan.tenantId}/subscriptions/${id}`
    );
    revalidatePath(
      `/tenant/${currentSubscription.plan.tenantId}/members/${currentSubscription.memberId}`
    );

    return { success: true, data: serializedSubscription };
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to cancel subscription",
    };
  }
}

/**
 * Get all subscriptions for a tenant
 * This function is cached to improve performance
 */
export const getSubscriptions = cache(async (tenantId: string) => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get all subscriptions for the tenant using Prisma
    const subscriptions = await prisma.subscription.findMany({
      where: {
        plan: {
          tenantId: tenantId,
        },
      },
      include: {
        member: true,
        plan: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedSubscriptions = subscriptions.map((subscription) => ({
      ...subscription,
      plan: {
        ...subscription.plan,
        price: Number(subscription.plan.price),
      },
    }));

    return { success: true, data: serializedSubscriptions };
  } catch (error) {
    console.error("Error getting subscriptions:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get subscriptions",
      data: [],
    };
  }
});

/**
 * Get a subscription by ID
 * This function is cached to improve performance
 * @param id The subscription ID
 * @param tenantId Optional tenant ID to verify the subscription belongs to this tenant
 */
export const getSubscriptionById = cache(
  async (id: string, tenantId?: string) => {
    try {
      // Build the Prisma query
      const whereClause: any = { id };

      // Get subscription with proper relationships using Prisma
      const subscription = await prisma.subscription.findUnique({
        where: whereClause,
        include: {
          member: {
            include: {
              tenantMembers: {
                include: {
                  tenant: true,
                },
              },
            },
          },
          plan: true,
          payments: {
            orderBy: {
              paymentDate: "desc",
            },
          },
        },
      });

      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // If tenantId is provided, verify the subscription belongs to this tenant
      if (tenantId && subscription.plan.tenantId !== tenantId) {
        throw new Error("Subscription not found in this tenant");
      }

      // Find the tenant member relationship for this specific tenant
      const tenantMember = subscription.member.tenantMembers.find(
        (tm) => tm.tenantId === subscription.plan.tenantId
      );

      // Transform the data to match the expected format in the client
      const transformedData = {
        ...subscription,
        plan: {
          ...subscription.plan,
          price: Number(subscription.plan.price),
        },
        payments: subscription.payments.map((payment) => ({
          ...payment,
          amount: Number(payment.amount),
        })),
        members: {
          id: subscription.member.id,
          first_name: subscription.member.firstName,
          last_name: subscription.member.lastName,
          email: subscription.member.email,
          tenant_id: tenantMember?.tenantId,
          status: tenantMember?.status,
        },
        subscription_plans: {
          id: subscription.plan.id,
          name: subscription.plan.name,
          price: Number(subscription.plan.price),
          billing_cycle: subscription.plan.billingCycle,
          tenant_id: subscription.plan.tenantId,
        },
      };

      return { success: true, data: transformedData };
    } catch (error) {
      console.error("Error getting subscription:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Failed to get subscription",
        data: null,
      };
    }
  }
);

/**
 * Get all subscriptions for a member in a specific tenant
 * This function is cached to improve performance
 */
export const getMemberSubscriptions = cache(
  async (memberId: string, tenantId?: string) => {
    try {
      // Build the Prisma query
      const whereClause: any = {
        memberId: memberId,
      };

      // If tenantId is provided, filter by tenant_id in subscription_plans
      if (tenantId) {
        whereClause.plan = {
          tenantId: tenantId,
        };
      }

      // Get subscriptions with proper relationships using Prisma
      const subscriptions = await prisma.subscription.findMany({
        where: whereClause,
        include: {
          plan: true,
          member: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Convert Decimal objects to numbers for client serialization
      const serializedSubscriptions = subscriptions.map((subscription) => ({
        ...subscription,
        plan: {
          ...subscription.plan,
          price: Number(subscription.plan.price),
        },
        // Add compatibility fields for existing client code
        subscription_plans: {
          id: subscription.plan.id,
          name: subscription.plan.name,
          price: Number(subscription.plan.price),
          billing_cycle: subscription.plan.billingCycle,
          tenant_id: subscription.plan.tenantId,
        },
        start_date: subscription.startDate,
        end_date: subscription.endDate,
        auto_renew: subscription.autoRenew,
      }));

      return { success: true, data: serializedSubscriptions };
    } catch (error) {
      console.error("Error getting member subscriptions:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get member subscriptions",
        data: [],
      };
    }
  }
);
