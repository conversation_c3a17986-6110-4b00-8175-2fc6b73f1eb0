"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { cache } from "react";
import { prisma } from "@/lib/prisma";
import { verifyTenantAccess } from "@/lib/auth-prisma";

// Define the plan schema for validation
const planSchema = z.object({
  tenant_id: z.string().min(1, "Tenant ID is required"),
  name: z.string().min(1, "Plan name is required"),
  description: z.string().optional(),
  price: z.number().min(0.01, "Price must be greater than 0"),
  billing_cycle: z.enum([
    "monthly",
    "quarterly",
    "yearly",
    "biannually",
    "annually",
    "biannual",
    "annual",
  ]),
  features: z.array(z.string()).optional(),
  is_active: z.boolean().default(true),
});

type PlanData = z.infer<typeof planSchema>;

/**
 * Create a new subscription plan
 */
export async function createPlan(data: PlanData) {
  try {
    // Validate the data
    const validatedData = planSchema.parse(data);

    // Verify tenant access
    await verifyTenantAccess(validatedData.tenant_id);

    // Create the plan using Prisma
    const plan = await prisma.subscriptionPlan.create({
      data: {
        tenantId: validatedData.tenant_id,
        name: validatedData.name,
        description: validatedData.description,
        price: validatedData.price,
        billingCycle: validatedData.billing_cycle,
        features: validatedData.features,
        isActive: validatedData.is_active,
      },
    });

    // Revalidate the plans page to update the UI
    revalidatePath(`/tenant/${plan.tenantId}/plans`);

    // Convert Decimal to number for client serialization
    const serializedPlan = {
      ...plan,
      price: Number(plan.price),
    };

    return { success: true, data: serializedPlan };
  } catch (error) {
    console.error("Error creating plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create plan",
    };
  }
}

/**
 * Update an existing subscription plan
 */
export async function updatePlan(
  id: string,
  tenantId: string,
  data: Partial<Omit<PlanData, "tenant_id">>
) {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Update the plan using Prisma
    const plan = await prisma.subscriptionPlan.update({
      where: {
        id,
        tenantId, // Ensure the plan belongs to the tenant
      },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.description !== undefined && {
          description: data.description,
        }),
        ...(data.price !== undefined && { price: data.price }),
        ...(data.billing_cycle && { billingCycle: data.billing_cycle }),
        ...(data.features !== undefined && { features: data.features }),
        ...(data.is_active !== undefined && { isActive: data.is_active }),
      },
    });

    // Revalidate the plan pages to update the UI
    revalidatePath(`/tenant/${tenantId}/plans`);
    revalidatePath(`/tenant/${tenantId}/plans/${id}`);

    // Convert Decimal to number for client serialization
    const serializedPlan = {
      ...plan,
      price: Number(plan.price),
    };

    return { success: true, data: serializedPlan };
  } catch (error) {
    console.error("Error updating plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update plan",
    };
  }
}

/**
 * Delete a subscription plan
 */
export async function deletePlan(id: string, tenantId: string) {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Delete the plan using Prisma
    await prisma.subscriptionPlan.delete({
      where: {
        id,
        tenantId, // Ensure the plan belongs to the tenant
      },
    });

    // Revalidate the plans page to update the UI
    revalidatePath(`/tenant/${tenantId}/plans`);

    return { success: true };
  } catch (error) {
    console.error("Error deleting plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete plan",
    };
  }
}

/**
 * Get all subscription plans for a tenant
 * This function is cached to improve performance
 */
export const getPlans = cache(async (tenantId: string) => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get all plans for the tenant using Prisma
    const plans = await prisma.subscriptionPlan.findMany({
      where: { tenantId },
      orderBy: { name: "asc" },
    });

    // Convert Decimal objects to numbers for client serialization
    const serializedPlans = plans.map((plan) => ({
      ...plan,
      price: Number(plan.price),
    }));

    return { success: true, data: serializedPlans };
  } catch (error) {
    console.error("Error getting plans:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plans",
      data: [],
    };
  }
});

/**
 * Get a subscription plan by ID
 * This function is cached to improve performance
 */
export const getPlanById = cache(async (id: string, tenantId: string) => {
  try {
    // Verify tenant access
    await verifyTenantAccess(tenantId);

    // Get the plan using Prisma
    const plan = await prisma.subscriptionPlan.findUnique({
      where: {
        id,
        tenantId, // Ensure the plan belongs to the tenant
      },
    });

    if (!plan) {
      throw new Error("Plan not found");
    }

    // Convert Decimal to number for client serialization
    const serializedPlan = {
      ...plan,
      price: Number(plan.price),
    };

    return { success: true, data: serializedPlan };
  } catch (error) {
    console.error("Error getting plan:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plan",
      data: null,
    };
  }
});
