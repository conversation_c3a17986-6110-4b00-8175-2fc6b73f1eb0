generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}



model Tenant {
  id                    String             @id @default(cuid())
  createdAt             DateTime           @default(now()) @map("created_at")
  name                  String
  slug                  String             @unique
  logoUrl               String?            @map("logo_url")
  ownerId               String             @map("owner_id")
  publicPageEnabled     Boolean            @default(false) @map("public_page_enabled")
  publicPageTitle       String?            @map("public_page_title")
  publicPageDescription String?            @map("public_page_description")
  subscriptionPlans     SubscriptionPlan[]
  tenantMembers         TenantMember[]
  tenantUsers           TenantUser[]

  @@map("tenants")
}

model TenantUser {
  id        String @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  tenantId  String @map("tenant_id")
  userId    String @map("user_id")
  role      String
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
  @@map("tenant_users")
}

model Member {
  id            String         @id @default(cuid())
  createdAt     DateTime       @default(now()) @map("created_at")
  firstName     String         @map("first_name")
  lastName      String         @map("last_name")
  email         String         @unique
  phone         String?        @unique
  authId        String?        @map("auth_id")
  passwordSet   Boolean        @default(false) @map("password_set")
  subscriptions Subscription[]
  tenantMembers TenantMember[]

  @@map("members")
}

model TenantMember {
  id        String @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  tenantId  String @map("tenant_id")
  memberId  String @map("member_id")
  status    String
  notes     String?
  member    Member @relation(fields: [memberId], references: [id], onDelete: Cascade)
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, memberId])
  @@map("tenant_members")
}

model SubscriptionPlan {
  id            String         @id @default(cuid())
  createdAt     DateTime       @default(now()) @map("created_at")
  tenantId      String         @map("tenant_id")
  name          String
  description   String?
  price         Decimal        @db.Decimal(10, 2)
  billingCycle  String         @map("billing_cycle")
  features      Json?
  isActive      Boolean        @default(true) @map("is_active")
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  subscriptions Subscription[]

  @@map("subscription_plans")
}

model Subscription {
  id        String           @id @default(cuid())
  createdAt DateTime         @default(now()) @map("created_at")
  memberId  String           @map("member_id")
  planId    String           @map("plan_id")
  startDate DateTime         @map("start_date")
  endDate   DateTime?        @map("end_date")
  status    String
  autoRenew Boolean          @default(true) @map("auto_renew")
  payments  Payment[]
  member    Member           @relation(fields: [memberId], references: [id], onDelete: Cascade)
  plan      SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model Payment {
  id             String       @id @default(cuid())
  createdAt      DateTime     @default(now()) @map("created_at")
  subscriptionId String       @map("subscription_id")
  amount         Decimal      @db.Decimal(10, 2)
  status         String
  paymentDate    DateTime     @map("payment_date")
  paymentMethod  String       @map("payment_method")
  transactionId  String?      @map("transaction_id")
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("payments")
}