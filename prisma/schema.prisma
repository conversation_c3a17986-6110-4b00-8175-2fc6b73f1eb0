generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["auth", "public"]
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model User {
  instance_id                 String?           @db.Uuid
  id                          String            @id @db.Uuid
  aud                         String?           @db.VarChar(255)
  role                        String?           @db.VarChar(255)
  email                       String?           @db.VarChar(255)
  encrypted_password          String?           @db.VarChar(255)
  email_confirmed_at          DateTime?         @db.Timestamptz(6)
  invited_at                  DateTime?         @db.Timestamptz(6)
  confirmation_token          String?           @db.Var<PERSON>har(255)
  confirmation_sent_at        DateTime?         @db.Timestamptz(6)
  recovery_token              String?           @db.Var<PERSON>har(255)
  recovery_sent_at            DateTime?         @db.Timestamptz(6)
  email_change_token_new      String?           @db.Var<PERSON>har(255)
  email_change                String?           @db.VarChar(255)
  email_change_sent_at        DateTime?         @db.Timestamptz(6)
  last_sign_in_at             DateTime?         @db.Timestamptz(6)
  raw_app_meta_data           Json?
  raw_user_meta_data          Json?
  is_super_admin              Boolean?
  createdAt                   DateTime?         @map("created_at") @db.Timestamptz(6)
  updatedAt                   DateTime?         @updatedAt @map("updated_at") @db.Timestamptz(6)
  phone                       String?           @unique
  phone_confirmed_at          DateTime?         @db.Timestamptz(6)
  phone_change                String?           @default("")
  phone_change_token          String?           @default("") @db.VarChar(255)
  phone_change_sent_at        DateTime?         @db.Timestamptz(6)
  confirmed_at                DateTime?         @default(dbgenerated("LEAST(email_confirmed_at, phone_confirmed_at)")) @db.Timestamptz(6)
  email_change_token_current  String?           @default("") @db.VarChar(255)
  email_change_confirm_status Int?              @default(0) @db.SmallInt
  banned_until                DateTime?         @db.Timestamptz(6)
  reauthentication_token      String?           @default("") @db.VarChar(255)
  reauthentication_sent_at    DateTime?         @db.Timestamptz(6)
  is_sso_user                 Boolean           @default(false)
  deleted_at                  DateTime?         @db.Timestamptz(6)
  is_anonymous                Boolean           @default(false)
  identities                  identities[]
  mfa_factors                 mfa_factors[]
  one_time_tokens             one_time_tokens[]
  sessions                    sessions[]

  @@index([instance_id])
  @@index([is_anonymous])
  @@map("users")
  @@schema("auth")
}

model Tenant {
  id                    String             @id @default(cuid())
  createdAt             DateTime           @default(now()) @map("created_at")
  name                  String
  slug                  String             @unique
  logoUrl               String?            @map("logo_url")
  ownerId               String             @map("owner_id")
  publicPageEnabled     Boolean            @default(false) @map("public_page_enabled")
  publicPageTitle       String?            @map("public_page_title")
  publicPageDescription String?            @map("public_page_description")
  subscriptionPlans     SubscriptionPlan[]
  tenantMembers         TenantMember[]
  tenantUsers           TenantUser[]
  owner                 users              @relation("TenantOwner", fields: [ownerId], references: [id], onDelete: Cascade)

  @@map("tenants")
  @@schema("public")
}

model TenantUser {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  tenantId  String   @map("tenant_id")
  userId    String   @map("user_id")
  role      String
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
  @@map("tenant_users")
  @@schema("public")
}

model Member {
  id            String         @id @default(cuid())
  createdAt     DateTime       @default(now()) @map("created_at")
  firstName     String         @map("first_name")
  lastName      String         @map("last_name")
  email         String         @unique
  phone         String?        @unique
  authId        String?        @map("auth_id")
  passwordSet   Boolean        @default(false) @map("password_set")
  authUser      users?         @relation("MemberAuth", fields: [authId], references: [id])
  subscriptions Subscription[]
  tenantMembers TenantMember[]

  @@map("members")
  @@schema("public")
}

model TenantMember {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  tenantId  String   @map("tenant_id")
  memberId  String   @map("member_id")
  status    String
  notes     String?
  member    Member   @relation(fields: [memberId], references: [id], onDelete: Cascade)
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, memberId])
  @@map("tenant_members")
  @@schema("public")
}

model SubscriptionPlan {
  id            String         @id @default(cuid())
  createdAt     DateTime       @default(now()) @map("created_at")
  tenantId      String         @map("tenant_id")
  name          String
  description   String?
  price         Decimal        @db.Decimal(10, 2)
  billingCycle  String         @map("billing_cycle")
  features      Json?
  isActive      Boolean        @default(true) @map("is_active")
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  subscriptions Subscription[]

  @@map("subscription_plans")
  @@schema("public")
}

model Subscription {
  id        String           @id @default(cuid())
  createdAt DateTime         @default(now()) @map("created_at")
  memberId  String           @map("member_id")
  planId    String           @map("plan_id")
  startDate DateTime         @map("start_date")
  endDate   DateTime?        @map("end_date")
  status    String
  autoRenew Boolean          @default(true) @map("auto_renew")
  payments  Payment[]
  member    Member           @relation(fields: [memberId], references: [id], onDelete: Cascade)
  plan      SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
  @@schema("public")
}

model Payment {
  id             String       @id @default(cuid())
  createdAt      DateTime     @default(now()) @map("created_at")
  subscriptionId String       @map("subscription_id")
  amount         Decimal      @db.Decimal(10, 2)
  status         String
  paymentDate    DateTime     @map("payment_date")
  paymentMethod  String       @map("payment_method")
  transactionId  String?      @map("transaction_id")
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("payments")
  @@schema("public")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model audit_log_entries {
  instance_id String?   @db.Uuid
  id          String    @id @db.Uuid
  payload     Json?     @db.Json
  created_at  DateTime? @db.Timestamptz(6)
  ip_address  String    @default("") @db.VarChar(64)

  @@index([instance_id], map: "audit_logs_instance_id_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model flow_state {
  id                     String                @id @db.Uuid
  user_id                String?               @db.Uuid
  auth_code              String
  code_challenge_method  code_challenge_method
  code_challenge         String
  provider_type          String
  provider_access_token  String?
  provider_refresh_token String?
  created_at             DateTime?             @db.Timestamptz(6)
  updated_at             DateTime?             @db.Timestamptz(6)
  authentication_method  String
  auth_code_issued_at    DateTime?             @db.Timestamptz(6)
  saml_relay_states      saml_relay_states[]

  @@index([created_at(sort: Desc)])
  @@index([auth_code], map: "idx_auth_code")
  @@index([user_id, authentication_method], map: "idx_user_id_auth_method")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model identities {
  provider_id     String
  user_id         String    @db.Uuid
  identity_data   Json
  provider        String
  last_sign_in_at DateTime? @db.Timestamptz(6)
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)
  email           String?   @default(dbgenerated("lower((identity_data ->> 'email'::text))"))
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  users           User      @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([provider_id, provider], map: "identities_provider_id_provider_unique")
  @@index([email])
  @@index([user_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model instances {
  id              String    @id @db.Uuid
  uuid            String?   @db.Uuid
  raw_base_config String?
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_amr_claims {
  session_id            String   @db.Uuid
  created_at            DateTime @db.Timestamptz(6)
  updated_at            DateTime @db.Timestamptz(6)
  authentication_method String
  id                    String   @id(map: "amr_id_pk") @db.Uuid
  sessions              sessions @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([session_id, authentication_method], map: "mfa_amr_claims_session_id_authentication_method_pkey")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_challenges {
  id                     String      @id @db.Uuid
  factor_id              String      @db.Uuid
  created_at             DateTime    @db.Timestamptz(6)
  verified_at            DateTime?   @db.Timestamptz(6)
  ip_address             String      @db.Inet
  otp_code               String?
  web_authn_session_data Json?
  mfa_factors            mfa_factors @relation(fields: [factor_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "mfa_challenges_auth_factor_id_fkey")

  @@index([created_at(sort: Desc)], map: "mfa_challenge_created_at_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_factors {
  id                   String           @id @db.Uuid
  user_id              String           @db.Uuid
  friendly_name        String?
  factor_type          factor_type
  status               factor_status
  created_at           DateTime         @db.Timestamptz(6)
  updated_at           DateTime         @db.Timestamptz(6)
  secret               String?
  phone                String?
  last_challenged_at   DateTime?        @unique @db.Timestamptz(6)
  web_authn_credential Json?
  web_authn_aaguid     String?          @db.Uuid
  mfa_challenges       mfa_challenges[]
  users                User             @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, phone], map: "unique_phone_factor_per_user")
  @@index([user_id, created_at], map: "factor_id_created_at_idx")
  @@index([user_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model one_time_tokens {
  id         String              @id @db.Uuid
  user_id    String              @db.Uuid
  token_type one_time_token_type
  token_hash String
  relates_to String
  created_at DateTime            @default(now()) @db.Timestamp(6)
  updated_at DateTime            @default(now()) @db.Timestamp(6)
  users      User                @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, token_type])
  @@index([relates_to], map: "one_time_tokens_relates_to_hash_idx", type: Hash)
  @@index([token_hash], map: "one_time_tokens_token_hash_hash_idx", type: Hash)
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model refresh_tokens {
  instance_id String?   @db.Uuid
  id          BigInt    @id @default(autoincrement())
  token       String?   @unique(map: "refresh_tokens_token_unique") @db.VarChar(255)
  user_id     String?   @db.VarChar(255)
  revoked     Boolean?
  created_at  DateTime? @db.Timestamptz(6)
  updated_at  DateTime? @db.Timestamptz(6)
  parent      String?   @db.VarChar(255)
  session_id  String?   @db.Uuid
  sessions    sessions? @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([instance_id])
  @@index([instance_id, user_id])
  @@index([parent])
  @@index([session_id, revoked])
  @@index([updated_at(sort: Desc)])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_providers {
  id                String        @id @db.Uuid
  sso_provider_id   String        @db.Uuid
  entity_id         String        @unique
  metadata_xml      String
  metadata_url      String?
  attribute_mapping Json?
  created_at        DateTime?     @db.Timestamptz(6)
  updated_at        DateTime?     @db.Timestamptz(6)
  name_id_format    String?
  sso_providers     sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_relay_states {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  request_id      String
  for_email       String?
  redirect_to     String?
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  flow_state_id   String?       @db.Uuid
  flow_state      flow_state?   @relation(fields: [flow_state_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([created_at(sort: Desc)])
  @@index([for_email])
  @@index([sso_provider_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schema_migrations {
  version String @id @db.VarChar(255)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model sessions {
  id             String           @id @db.Uuid
  user_id        String           @db.Uuid
  created_at     DateTime?        @db.Timestamptz(6)
  updated_at     DateTime?        @db.Timestamptz(6)
  factor_id      String?          @db.Uuid
  aal            aal_level?
  not_after      DateTime?        @db.Timestamptz(6)
  refreshed_at   DateTime?        @db.Timestamp(6)
  user_agent     String?
  ip             String?          @db.Inet
  tag            String?
  mfa_amr_claims mfa_amr_claims[]
  refresh_tokens refresh_tokens[]
  users          User             @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([not_after(sort: Desc)])
  @@index([user_id])
  @@index([user_id, created_at], map: "user_id_created_at_idx")
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_domains {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  domain          String
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_providers {
  id                String              @id @db.Uuid
  resource_id       String?
  created_at        DateTime?           @db.Timestamptz(6)
  updated_at        DateTime?           @db.Timestamptz(6)
  saml_providers    saml_providers[]
  saml_relay_states saml_relay_states[]
  sso_domains       sso_domains[]

  @@schema("auth")
}

model users {
  id           String       @id
  email        String?      @unique
  created_at   DateTime     @default(now())
  updated_at   DateTime     @default(now())
  members      Member[]     @relation("MemberAuth")
  tenantUsers  TenantUser[]
  ownedTenants Tenant[]     @relation("TenantOwner")

  @@schema("public")
}

enum aal_level {
  aal1
  aal2
  aal3

  @@schema("auth")
}

enum code_challenge_method {
  s256
  plain

  @@schema("auth")
}

enum factor_status {
  unverified
  verified

  @@schema("auth")
}

enum factor_type {
  totp
  webauthn
  phone

  @@schema("auth")
}

enum one_time_token_type {
  confirmation_token
  reauthentication_token
  recovery_token
  email_change_token_new
  email_change_token_current
  phone_change_token

  @@schema("auth")
}
